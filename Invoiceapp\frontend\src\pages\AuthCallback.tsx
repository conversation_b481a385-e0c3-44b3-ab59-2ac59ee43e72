import { useEffect } from 'react';
import { handleAuthCallback } from '@/utils/oneDriveAuth';

const AuthCallback = () => {
    useEffect(() => {
        // Handle the authentication callback
        handleAuthCallback();
    }, []);

    return (
        <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Processing authentication...</p>
            </div>
        </div>
    );
};

export default AuthCallback;
