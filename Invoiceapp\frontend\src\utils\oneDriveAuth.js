// OneDrive Authentication Utility - Alternative Implementation
// This uses a popup-based approach that works with Web App registrations

// Configuration
const config = {
    clientId: '86756722-ad2a-4ac0-8806-e2705653949a',
    tenantId: '14158288-a340-4380-88ed-a8989a932425',
    redirectUri: 'http://localhost:3000/auth-callback',
    scopes: ['Files.ReadWrite', 'User.Read'],
};

// Helper function to generate random state for security
const generateState = () => {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

// Helper function to build authorization URL
const buildAuthUrl = () => {
    const state = generateState();
    sessionStorage.setItem('oauth_state', state);

    const params = new URLSearchParams({
        client_id: config.clientId,
        response_type: 'code',
        redirect_uri: config.redirectUri,
        scope: config.scopes.join(' '),
        state: state,
        prompt: 'select_account',
    });

    return `https://login.microsoftonline.com/${config.tenantId}/oauth2/v2.0/authorize?${params.toString()}`;
};

/**
 * Sign in to Microsoft and get OneDrive access token using popup
 */
export const signInToOneDrive = async () => {
    try {
        // Check if we already have a token
        const existingToken = localStorage.getItem('onedrive_access_token');
        const tokenExpiry = localStorage.getItem('onedrive_token_expiry');

        if (existingToken && tokenExpiry && new Date().getTime() < parseInt(tokenExpiry)) {
            return {
                success: true,
                accessToken: existingToken,
            };
        }

        // Open popup for authentication
        const authUrl = buildAuthUrl();
        const popup = window.open(authUrl, 'microsoft-auth', 'width=500,height=600,scrollbars=yes,resizable=yes');

        if (!popup) {
            throw new Error('Popup blocked. Please allow popups for this site.');
        }

        // Wait for popup to complete authentication
        return new Promise((resolve, reject) => {
            const checkClosed = setInterval(() => {
                if (popup.closed) {
                    clearInterval(checkClosed);

                    // Check if we got a token
                    const token = localStorage.getItem('onedrive_access_token');
                    if (token) {
                        resolve({
                            success: true,
                            accessToken: token,
                        });
                    } else {
                        reject(new Error('Authentication was cancelled or failed'));
                    }
                }
            }, 1000);

            // Timeout after 5 minutes
            setTimeout(() => {
                clearInterval(checkClosed);
                if (!popup.closed) {
                    popup.close();
                }
                reject(new Error('Authentication timeout'));
            }, 300000);
        });

    } catch (error) {
        console.error('OneDrive sign-in failed:', error);
        return {
            success: false,
            error: error.message,
        };
    }
};

/**
 * Get OneDrive access token (from storage)
 */
export const getOneDriveToken = async () => {
    try {
        const token = localStorage.getItem('onedrive_access_token');
        const tokenExpiry = localStorage.getItem('onedrive_token_expiry');

        if (!token) {
            return {
                success: false,
                error: 'No access token found. Please sign in first.',
            };
        }

        if (tokenExpiry && new Date().getTime() >= parseInt(tokenExpiry)) {
            // Token expired
            localStorage.removeItem('onedrive_access_token');
            localStorage.removeItem('onedrive_token_expiry');
            return {
                success: false,
                error: 'Access token expired. Please sign in again.',
            };
        }

        return {
            success: true,
            accessToken: token,
        };
    } catch (error) {
        console.error('Failed to get OneDrive token:', error);
        return {
            success: false,
            error: error.message,
        };
    }
};

/**
 * Sign out from Microsoft
 */
export const signOutFromOneDrive = async () => {
    try {
        // Clear stored tokens
        localStorage.removeItem('onedrive_access_token');
        localStorage.removeItem('onedrive_token_expiry');
        localStorage.removeItem('onedrive_user_info');

        return { success: true };
    } catch (error) {
        console.error('OneDrive sign-out failed:', error);
        return {
            success: false,
            error: error.message,
        };
    }
};

/**
 * Check if user is signed in to OneDrive
 */
export const isSignedInToOneDrive = async () => {
    try {
        const token = localStorage.getItem('onedrive_access_token');
        const tokenExpiry = localStorage.getItem('onedrive_token_expiry');

        if (!token) {
            return false;
        }

        if (tokenExpiry && new Date().getTime() >= parseInt(tokenExpiry)) {
            // Token expired, clean up
            localStorage.removeItem('onedrive_access_token');
            localStorage.removeItem('onedrive_token_expiry');
            localStorage.removeItem('onedrive_user_info');
            return false;
        }

        return true;
    } catch (error) {
        console.error('Failed to check OneDrive sign-in status:', error);
        return false;
    }
};

/**
 * Get current user info
 */
export const getCurrentUser = async () => {
    try {
        const userInfo = localStorage.getItem('onedrive_user_info');

        if (userInfo) {
            return {
                success: true,
                user: JSON.parse(userInfo),
            };
        }

        return {
            success: false,
            error: 'No user signed in',
        };
    } catch (error) {
        console.error('Failed to get current user:', error);
        return {
            success: false,
            error: error.message,
        };
    }
};

/**
 * Handle the authentication callback (to be called from the popup)
 */
export const handleAuthCallback = () => {
    try {
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const state = urlParams.get('state');
        const error = urlParams.get('error');

        if (error) {
            console.error('Authentication error:', error);
            window.close();
            return;
        }

        if (!code || !state) {
            console.error('Missing code or state parameter');
            window.close();
            return;
        }

        // Verify state
        const storedState = sessionStorage.getItem('oauth_state');
        if (state !== storedState) {
            console.error('State mismatch');
            window.close();
            return;
        }

        // Exchange code for token (this would normally be done on the backend)
        // For now, we'll use a simplified approach
        exchangeCodeForToken(code);

    } catch (error) {
        console.error('Error handling auth callback:', error);
        window.close();
    }
};

/**
 * Exchange authorization code for access token
 */
const exchangeCodeForToken = async (code) => {
    try {
        // Send the code to backend for token exchange
        const response = await fetch('http://localhost:8080/api/invoices/onedrive/auth/token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Basic ' + btoa('admin:admin123')
            },
            body: JSON.stringify({
                code: code,
                state: sessionStorage.getItem('oauth_state')
            })
        });

        if (!response.ok) {
            throw new Error('Failed to exchange code for token');
        }

        const result = await response.json();

        if (result.success) {
            const expiryTime = new Date().getTime() + (result.expires_in * 1000);

            localStorage.setItem('onedrive_access_token', result.access_token);
            localStorage.setItem('onedrive_token_expiry', expiryTime.toString());

            // Also get user info
            await getUserInfo(result.access_token);

            window.close();
        } else {
            throw new Error(result.error || 'Token exchange failed');
        }

    } catch (error) {
        console.error('Error exchanging code for token:', error);
        window.close();
    }
};

/**
 * Get user information from Microsoft Graph
 */
const getUserInfo = async (accessToken) => {
    try {
        const response = await fetch('https://graph.microsoft.com/v1.0/me', {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (response.ok) {
            const userInfo = await response.json();
            localStorage.setItem('onedrive_user_info', JSON.stringify(userInfo));
        }
    } catch (error) {
        console.error('Failed to get user info:', error);
    }
};
