// OneDrive Authentication Utility - Alternative Implementation
// This uses a popup-based approach that works with Web App registrations

// Configuration
const config = {
    clientId: '86756722-ad2a-4ac0-8806-e2705653949a',
    tenantId: '14158288-a340-4380-88ed-a8989a932425',
    redirectUri: 'http://localhost:3000',
    scopes: ['Files.ReadWrite', 'User.Read'],
};

// Helper function to generate random state for security
const generateState = () => {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

// Helper function to build authorization URL
const buildAuthUrl = () => {
    const state = generateState();
    sessionStorage.setItem('oauth_state', state);

    const params = new URLSearchParams({
        client_id: config.clientId,
        response_type: 'code',
        redirect_uri: config.redirectUri,
        scope: config.scopes.join(' '),
        state: state,
        prompt: 'select_account',
    });

    return `https://login.microsoftonline.com/${config.tenantId}/oauth2/v2.0/authorize?${params.toString()}`;
};

/**
 * Sign in to Microsoft and get OneDrive access token using popup
 */
export const signInToOneDrive = async () => {
    try {
        // Check if we already have a token
        const existingToken = localStorage.getItem('onedrive_access_token');
        const tokenExpiry = localStorage.getItem('onedrive_token_expiry');

        if (existingToken && tokenExpiry && new Date().getTime() < parseInt(tokenExpiry)) {
            return {
                success: true,
                accessToken: existingToken,
            };
        }

        // Check if we're returning from auth (URL has code parameter)
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const state = urlParams.get('state');

        if (code && state) {
            // We're returning from authentication, handle the callback
            const result = await handleAuthCallbackInline(code, state);

            // Clean up URL parameters
            window.history.replaceState({}, document.title, window.location.pathname);

            return result;
        }

        // Open popup for authentication
        const authUrl = buildAuthUrl();

        // Redirect to Microsoft auth instead of popup to avoid CORS issues
        window.location.href = authUrl;

        // Return a pending state since we're redirecting
        return {
            success: false,
            pending: true,
            message: 'Redirecting to Microsoft authentication...',
        };

    } catch (error) {
        console.error('OneDrive sign-in failed:', error);
        return {
            success: false,
            error: error.message,
        };
    }
};

/**
 * Get OneDrive access token (from storage)
 */
export const getOneDriveToken = async () => {
    try {
        const token = localStorage.getItem('onedrive_access_token');
        const tokenExpiry = localStorage.getItem('onedrive_token_expiry');

        if (!token) {
            return {
                success: false,
                error: 'No access token found. Please sign in first.',
            };
        }

        if (tokenExpiry && new Date().getTime() >= parseInt(tokenExpiry)) {
            // Token expired
            localStorage.removeItem('onedrive_access_token');
            localStorage.removeItem('onedrive_token_expiry');
            return {
                success: false,
                error: 'Access token expired. Please sign in again.',
            };
        }

        return {
            success: true,
            accessToken: token,
        };
    } catch (error) {
        console.error('Failed to get OneDrive token:', error);
        return {
            success: false,
            error: error.message,
        };
    }
};

/**
 * Sign out from Microsoft
 */
export const signOutFromOneDrive = async () => {
    try {
        // Clear stored tokens
        localStorage.removeItem('onedrive_access_token');
        localStorage.removeItem('onedrive_token_expiry');
        localStorage.removeItem('onedrive_user_info');

        return { success: true };
    } catch (error) {
        console.error('OneDrive sign-out failed:', error);
        return {
            success: false,
            error: error.message,
        };
    }
};

/**
 * Check if user is signed in to OneDrive
 */
export const isSignedInToOneDrive = async () => {
    try {
        const token = localStorage.getItem('onedrive_access_token');
        const tokenExpiry = localStorage.getItem('onedrive_token_expiry');

        if (!token) {
            return false;
        }

        if (tokenExpiry && new Date().getTime() >= parseInt(tokenExpiry)) {
            // Token expired, clean up
            localStorage.removeItem('onedrive_access_token');
            localStorage.removeItem('onedrive_token_expiry');
            localStorage.removeItem('onedrive_user_info');
            return false;
        }

        return true;
    } catch (error) {
        console.error('Failed to check OneDrive sign-in status:', error);
        return false;
    }
};

/**
 * Get current user info
 */
export const getCurrentUser = async () => {
    try {
        const userInfo = localStorage.getItem('onedrive_user_info');

        if (userInfo) {
            return {
                success: true,
                user: JSON.parse(userInfo),
            };
        }

        return {
            success: false,
            error: 'No user signed in',
        };
    } catch (error) {
        console.error('Failed to get current user:', error);
        return {
            success: false,
            error: error.message,
        };
    }
};

/**
 * Handle the authentication callback inline (when returning to the same page)
 */
const handleAuthCallbackInline = async (code, state) => {
    try {
        // Verify state
        const storedState = sessionStorage.getItem('oauth_state');
        if (state !== storedState) {
            throw new Error('State mismatch - possible security issue');
        }

        // Exchange code for token
        const result = await exchangeCodeForToken(code);
        return result;

    } catch (error) {
        console.error('Error handling auth callback:', error);
        return {
            success: false,
            error: error.message,
        };
    }
};

/**
 * Handle the authentication callback (to be called from the popup - legacy)
 */
export const handleAuthCallback = () => {
    try {
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const state = urlParams.get('state');
        const error = urlParams.get('error');

        if (error) {
            console.error('Authentication error:', error);
            return;
        }

        if (!code || !state) {
            console.error('Missing code or state parameter');
            return;
        }

        // Verify state
        const storedState = sessionStorage.getItem('oauth_state');
        if (state !== storedState) {
            console.error('State mismatch');
            return;
        }

        // Exchange code for token
        exchangeCodeForToken(code);

    } catch (error) {
        console.error('Error handling auth callback:', error);
    }
};

/**
 * Exchange authorization code for access token
 */
const exchangeCodeForToken = async (code) => {
    try {
        // Send the code to backend for token exchange
        const response = await fetch('http://localhost:8080/api/invoices/onedrive/auth/token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Basic ' + btoa('admin:admin123')
            },
            body: JSON.stringify({
                code: code,
                state: sessionStorage.getItem('oauth_state')
            })
        });

        if (!response.ok) {
            throw new Error('Failed to exchange code for token');
        }

        const result = await response.json();

        if (result.success) {
            const expiryTime = new Date().getTime() + (result.expires_in * 1000);

            localStorage.setItem('onedrive_access_token', result.access_token);
            localStorage.setItem('onedrive_token_expiry', expiryTime.toString());

            // Also get user info
            await getUserInfo(result.access_token);

            return {
                success: true,
                accessToken: result.access_token,
            };
        } else {
            throw new Error(result.error || 'Token exchange failed');
        }

    } catch (error) {
        console.error('Error exchanging code for token:', error);
        return {
            success: false,
            error: error.message,
        };
    }
};

/**
 * Get user information from Microsoft Graph
 */
const getUserInfo = async (accessToken) => {
    try {
        const response = await fetch('https://graph.microsoft.com/v1.0/me', {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (response.ok) {
            const userInfo = await response.json();
            localStorage.setItem('onedrive_user_info', JSON.stringify(userInfo));
        }
    } catch (error) {
        console.error('Failed to get user info:', error);
    }
};
