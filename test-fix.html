<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Data Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        button:hover {
            background-color: #0056b3;
        }
        #result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 500px;
            overflow-y: auto;
        }
        .loading {
            color: #007bff;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <h1>Invoice Data Fix Test</h1>
    <p>This page allows you to test the invoice data fix endpoint.</p>
    
    <button onclick="fixInvoiceData()">Fix Invoice Data</button>
    <button onclick="testConnection()">Test Backend Connection</button>
    <button onclick="testInvoiceUpdate()">Test Invoice Update</button>
    
    <div id="result"></div>

    <script>
        async function fixInvoiceData() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<span class="loading">Fixing invoice data...</span>';
            
            try {
                const response = await fetch('http://localhost:8080/api/fix/invoice-data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                const result = await response.text();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<span class="success">SUCCESS:</span>\n${result}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">ERROR (${response.status}):</span>\n${result}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">NETWORK ERROR:</span>\n${error.message}`;
            }
        }
        
        async function testConnection() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<span class="loading">Testing connection...</span>';

            try {
                const response = await fetch('http://localhost:8080/api/fix/test', {
                    method: 'GET',
                });

                const result = await response.text();

                if (response.ok) {
                    resultDiv.innerHTML = `<span class="success">CONNECTION SUCCESS:</span>\n${result}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">CONNECTION ERROR (${response.status}):</span>\n${result}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">CONNECTION FAILED:</span>\n${error.message}`;
            }
        }

        async function testInvoiceUpdate() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<span class="loading">Testing invoice update...</span>';

            // First, get the list of invoices to find one to update
            try {
                const listResponse = await fetch('http://localhost:8080/api/invoices', {
                    method: 'POST',
                });

                if (!listResponse.ok) {
                    resultDiv.innerHTML = `<span class="error">Failed to get invoices (${listResponse.status})</span>`;
                    return;
                }

                const invoices = await listResponse.json();

                if (!invoices || invoices.length === 0) {
                    resultDiv.innerHTML = `<span class="error">No invoices found to test update</span>`;
                    return;
                }

                const firstInvoice = invoices[0];
                resultDiv.innerHTML = `<span class="loading">Found invoice ${firstInvoice.id}, testing update...</span>`;

                // Test updating the first invoice
                const updateData = {
                    invoiceNumber: firstInvoice.invoiceNumber + "_UPDATED",
                    rate: 55000.00,
                    billingAmount: 55000.00,
                    taxAmount: 9900.00,
                    totalAmount: 64900.00,
                    attendanceDays: 25
                };

                const updateResponse = await fetch(`http://localhost:8080/api/invoices/update/${firstInvoice.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(updateData)
                });

                const updateResult = await updateResponse.text();

                if (updateResponse.ok) {
                    resultDiv.innerHTML = `<span class="success">INVOICE UPDATE SUCCESS:</span>\nUpdated invoice ${firstInvoice.id}\nResponse: ${updateResult}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">INVOICE UPDATE ERROR (${updateResponse.status}):</span>\n${updateResult}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">INVOICE UPDATE FAILED:</span>\n${error.message}`;
            }
        }
    </script>
</body>
</html>
