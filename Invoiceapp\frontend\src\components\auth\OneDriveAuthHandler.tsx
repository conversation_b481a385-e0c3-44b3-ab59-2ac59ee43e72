import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { handleAuthCallback } from '@/utils/oneDriveAuth';

const OneDriveAuthHandler = () => {
    const navigate = useNavigate();
    const location = useLocation();

    useEffect(() => {
        const handleOneDriveAuth = async () => {
            const urlParams = new URLSearchParams(location.search);
            const code = urlParams.get('code');
            const state = urlParams.get('state');
            
            if (code && state) {
                try {
                    // Handle the authentication callback
                    handleAuthCallback();
                    
                    // Get the return path from session storage
                    const returnPath = sessionStorage.getItem('onedrive_return_path');
                    
                    // Clean up URL parameters and navigate back
                    if (returnPath && returnPath !== '/') {
                        navigate(returnPath, { replace: true });
                    } else {
                        navigate('/invoices', { replace: true });
                    }
                    
                    // Clean up session storage
                    sessionStorage.removeItem('onedrive_return_path');
                    
                } catch (error) {
                    console.error('Error handling OneDrive authentication:', error);
                    // Navigate to invoices page on error
                    navigate('/invoices', { replace: true });
                }
            }
        };

        handleOneDriveAuth();
    }, [location.search, navigate]);

    return null; // This component doesn't render anything
};

export default OneDriveAuthHandler;
