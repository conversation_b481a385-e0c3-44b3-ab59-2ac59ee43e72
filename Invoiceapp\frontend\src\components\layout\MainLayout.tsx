
import React from "react";
import { Outlet } from "react-router-dom";
import Header from "@/components/layout/Header";
import ResponsiveSidebar from "@/components/layout/ResponsiveSidebar";
import OneDriveAuthHandler from "@/components/auth/OneDriveAuthHandler";

const MainLayout = () => {
  // Sidebar is now always open
  const sidebarOpen = true;

  return (
    <div className="flex min-h-screen w-full bg-background">
      <ResponsiveSidebar isOpen={sidebarOpen} toggleSidebar={() => {}} />

      <div className="flex-1 flex flex-col min-h-screen transition-all duration-300 md:ml-64">
        <Header />
        <main className="flex-1 overflow-auto bg-background p-4 md:p-6">
          <div className="mx-auto max-w-7xl">
            <OneDriveAuthHandler />
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};

export default MainLayout;
