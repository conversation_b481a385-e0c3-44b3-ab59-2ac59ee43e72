import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'sonner';

const OneDriveAuthHandler = () => {
    const navigate = useNavigate();
    const location = useLocation();

    useEffect(() => {
        const handleOneDriveAuth = async () => {
            const urlParams = new URLSearchParams(location.search);
            const code = urlParams.get('code');
            const state = urlParams.get('state');

            if (code && state) {
                try {
                    console.log('OneDrive authentication callback received');

                    // Exchange code for token using existing endpoint
                    const tokenResponse = await fetch('http://localhost:8080/api/invoices/onedrive/auth/token', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Basic ' + btoa('admin:admin123')
                        },
                        body: JSON.stringify({ code, state })
                    });

                    if (!tokenResponse.ok) {
                        throw new Error('Failed to exchange code for token');
                    }

                    const tokenData = await tokenResponse.json();
                    console.log('Token exchange successful');

                    // Store token for later use
                    localStorage.setItem('onedrive_access_token', tokenData.accessToken);
                    localStorage.setItem('onedrive_token_expiry', (Date.now() + (tokenData.expiresIn * 1000)).toString());

                    // Check for pending uploads
                    const pendingUploads = localStorage.getItem('pending_onedrive_upload');
                    if (pendingUploads) {
                        const invoiceIds = JSON.parse(pendingUploads);
                        localStorage.removeItem('pending_onedrive_upload');

                        toast.success('Authentication successful! Starting upload...');

                        // Trigger upload
                        await uploadInvoicesToOneDrive(invoiceIds, tokenData.accessToken);
                    } else {
                        toast.success('Successfully authenticated with OneDrive!');
                    }

                    // Clean up URL and navigate to invoices
                    window.history.replaceState({}, document.title, '/invoices');
                    navigate('/invoices', { replace: true });

                } catch (error) {
                    console.error('Error handling OneDrive authentication:', error);
                    toast.error('Failed to authenticate with OneDrive');
                    // Clean up URL and navigate to invoices
                    window.history.replaceState({}, document.title, '/invoices');
                    navigate('/invoices', { replace: true });
                }
            }
        };

        handleOneDriveAuth();
    }, [location.search, navigate]);

    const uploadInvoicesToOneDrive = async (invoiceIds: string[], accessToken: string) => {
        const toastId = toast.loading(`Uploading ${invoiceIds.length} invoice(s) to OneDrive...`);

        let successCount = 0;
        let failCount = 0;

        for (const invoiceId of invoiceIds) {
            try {
                console.log(`Uploading invoice ${invoiceId} to OneDrive`);

                const response = await fetch(`http://localhost:8080/api/invoices/onedrive/upload/${invoiceId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Basic ' + btoa('admin:admin123'),
                        'X-OneDrive-Token': accessToken
                    }
                });

                if (!response.ok) {
                    throw new Error(`Failed to upload invoice ${invoiceId}`);
                }

                successCount++;
                toast.loading(`Uploaded ${successCount} of ${invoiceIds.length} invoices...`, { id: toastId });

            } catch (error) {
                console.error(`Error uploading invoice ${invoiceId}:`, error);
                failCount++;
            }
        }

        toast.dismiss(toastId);

        if (successCount > 0 && failCount === 0) {
            toast.success(`Successfully uploaded ${successCount} invoice(s) to OneDrive!`);
        } else if (successCount > 0 && failCount > 0) {
            toast.warning(`Uploaded ${successCount} invoice(s). ${failCount} failed.`);
        } else {
            toast.error('Failed to upload invoices to OneDrive');
        }
    };

    return null; // This component doesn't render anything
};

export default OneDriveAuthHandler;
